import { But<PERSON> } from "@/components/ui/button";
import { FileQuestion, Home, Search } from "lucide-react";
import Link from "next/link";

export default function NotFound() {
  return (
    <div className="h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
      {/* Beautiful gradient background matching home page style */}
      <div className="absolute -top-20 left-1/2 -translate-x-1/2 h-[400px] w-[400px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-30 blur-3xl rounded-full -z-10"></div>
      <div className="absolute top-1/3 right-1/4 h-[300px] w-[300px] bg-gradient-to-br from-purple-800/40 to-cyan-800/40 opacity-20 blur-3xl rounded-full -z-10"></div>

      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move-hero"
        style={{
          backgroundSize: "20px 20px",
        }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 via-transparent to-blue-900/25" />

      {/* Main content */}
      <div className="relative z-10 h-full flex items-center justify-center px-4">
        <div className="max-w-lg w-full text-center space-y-6">
          {/* 404 Icon and Number */}
          <div className="space-y-3">
            <div className="flex justify-center">
              <FileQuestion className="w-16 h-16 text-blue-400/80 animate-pulse" />
            </div>

            <div className="space-y-2">
              <h1 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
                404
              </h1>
              <div className="h-1 w-24 mx-auto bg-gradient-to-r from-blue-500 via-cyan-400 to-blue-600 rounded-full"></div>
            </div>
          </div>

          {/* Error Message */}
          <div className="space-y-3">
            <h2 className="text-2xl md:text-3xl font-bold text-white">
              Page Not Found
            </h2>
            <p className="text-gray-300 max-w-md mx-auto">
              The page you're looking for doesn't exist. Let's get you back on
              track.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center pt-4">
            <Link href="/">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2"
              >
                <Home className="w-4 h-4" />
                Go Home
              </Button>
            </Link>

            <Link href="/sheetscope">
              <Button
                variant="outline"
                size="lg"
                className="border-gray-600 text-gray-300 hover:text-white hover:bg-gray-800/50 font-medium px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2"
              >
                <Search className="w-4 h-4" />
                Explore SheetScope
              </Button>
            </Link>
          </div>

          {/* Branding */}
          <div className="pt-6">
            <p className="text-gray-500 text-sm">
              <span className="font-semibold text-white">MyCPTrainer</span> -
              Your AI-powered CP Trainer
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
