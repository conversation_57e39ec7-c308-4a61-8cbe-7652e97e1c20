import { But<PERSON> } from "@/components/ui/button";
import { FileQuestion, Home, Search } from "lucide-react";
import Link from "next/link";

export default function NotFound() {
  return (
    <div className="min-h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden mt-16">
      {/* Beautiful gradient background matching home page style */}
      <div className="absolute -top-20 left-1/2 -translate-x-1/2 h-[600px] w-[600px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-30 blur-3xl rounded-full -z-10"></div>
      <div className="absolute top-1/3 right-1/4 h-[400px] w-[400px] bg-gradient-to-br from-purple-800/40 to-cyan-800/40 opacity-20 blur-3xl rounded-full -z-10"></div>
      <div className="absolute bottom-1/4 left-1/4 h-[500px] w-[500px] bg-gradient-to-br from-emerald-800/30 to-blue-800/30 opacity-15 blur-3xl rounded-full -z-10"></div>

      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move-hero"
        style={{
          backgroundSize: "20px 20px",
        }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 via-transparent to-blue-900/25" />

      {/* Main content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="max-w-2xl w-full text-center space-y-8">
          {/* 404 Icon and Number */}
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="relative">
                <FileQuestion className="w-24 h-24 text-blue-400/80 animate-pulse" />
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-500/20 border-2 border-red-400/60 rounded-full flex items-center justify-center">
                  <span className="text-red-400 text-xs font-bold">!</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h1 className="text-8xl md:text-9xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
                404
              </h1>
              <div className="h-1 w-32 mx-auto bg-gradient-to-r from-blue-500 via-cyan-400 to-blue-600 rounded-full"></div>
            </div>
          </div>

          {/* Error Message */}
          <div className="space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Page Not Found
            </h2>
            <p className="text-lg text-gray-300 max-w-lg mx-auto leading-relaxed">
              The page you're looking for doesn't exist or has been moved. Let's
              get you back to exploring your competitive programming journey.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <Link href="/">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl"
              >
                <Home className="w-5 h-5" />
                Go Home
              </Button>
            </Link>

            <Link href="/sheetscope">
              <Button
                variant="outline"
                size="lg"
                className="border-gray-600 text-gray-300 hover:text-white hover:bg-gray-800/50 font-medium px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2"
              >
                <Search className="w-5 h-5" />
                Explore SheetScope
              </Button>
            </Link>
          </div>

          {/* Quick Navigation */}
          <div className="pt-12 border-t border-gray-800/50">
            <p className="text-sm text-gray-400 mb-6">
              Or explore these popular sections:
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              <Link
                href="/dashboard"
                className="group p-4 rounded-lg bg-gray-900/30 border border-gray-800/50 hover:border-blue-500/50 hover:bg-gray-800/40 transition-all duration-300"
              >
                <div className="text-blue-400 group-hover:text-blue-300 text-sm font-medium">
                  Dashboard
                </div>
              </Link>
              <Link
                href="/visualize"
                className="group p-4 rounded-lg bg-gray-900/30 border border-gray-800/50 hover:border-cyan-500/50 hover:bg-gray-800/40 transition-all duration-300"
              >
                <div className="text-cyan-400 group-hover:text-cyan-300 text-sm font-medium">
                  Visualize
                </div>
              </Link>
              <Link
                href="/profile"
                className="group p-4 rounded-lg bg-gray-900/30 border border-gray-800/50 hover:border-purple-500/50 hover:bg-gray-800/40 transition-all duration-300"
              >
                <div className="text-purple-400 group-hover:text-purple-300 text-sm font-medium">
                  Profile
                </div>
              </Link>
              <Link
                href="/customsheet"
                className="group p-4 rounded-lg bg-gray-900/30 border border-gray-800/50 hover:border-emerald-500/50 hover:bg-gray-800/40 transition-all duration-300"
              >
                <div className="text-emerald-400 group-hover:text-emerald-300 text-sm font-medium">
                  Custom Sheet
                </div>
              </Link>
            </div>
          </div>

          {/* Branding */}
          <div className="pt-8">
            <p className="text-gray-500 text-sm">
              <span className="font-semibold text-white">MyCPTrainer</span> -
              Your AI-powered CP Trainer
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
